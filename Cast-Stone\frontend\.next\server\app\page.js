/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCatalogSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCollectionsGrid.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CFeaturedCollections.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHomePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CTestimonialsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCatalogSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCollectionsGrid.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CFeaturedCollections.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHomePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CTestimonialsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(rsc)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(rsc)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(rsc)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(rsc)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(rsc)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(rsc)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(rsc)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navigation.tsx */ \"(rsc)/./src/components/layout/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCatalogSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCollectionsGrid.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CFeaturedCollections.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHomePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CTestimonialsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7dc8ac8dc5b2\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3ZGM4YWM4ZGM1YjJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Cast Stone Interiors & Decorations - Timeless Elegance\",\n    description: \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\",\n    keywords: \"cast stone, architectural elements, fireplaces, decorative pieces, interior design, handcrafted stone\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components */ \"(rsc)/./src/components/index.ts\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_1__.HomePage, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXlDO0FBRTFCLFNBQVNDO0lBQ3RCLHFCQUFPLDhEQUFDRCxpREFBUUE7Ozs7O0FBQ2xCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSG9tZVBhZ2UgfSBmcm9tICcuLi9jb21wb25lbnRzJztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XHJcbiAgcmV0dXJuIDxIb21lUGFnZSAvPjtcclxufVxyXG4iXSwibmFtZXMiOlsiSG9tZVBhZ2UiLCJIb21lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/home/<USER>":
/*!************************************************!*\
  !*** ./src/components/home/<USER>
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\CatalogSection.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\CollectionsGrid.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/home/<USER>":
/*!*****************************************************!*\
  !*** ./src/components/home/<USER>
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\FeaturedCollections.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/home/<USER>":
/*!*********************************************!*\
  !*** ./src/components/home/<USER>
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\HeroSection.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/home/<USER>":
/*!******************************************!*\
  !*** ./src/components/home/<USER>
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\HomePage.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/home/<USER>":
/*!*****************************************************!*\
  !*** ./src/components/home/<USER>
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\home\\TestimonialsSection.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/index.ts":
/*!*********************************!*\
  !*** ./src/components/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CatalogSection: () => (/* reexport safe */ _home_CatalogSection__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   CollectionsGrid: () => (/* reexport safe */ _home_CollectionsGrid__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   FeaturedCollections: () => (/* reexport safe */ _home_FeaturedCollections__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Footer: () => (/* reexport safe */ _layout_Footer__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   HeroSection: () => (/* reexport safe */ _home_HeroSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   HomePage: () => (/* reexport safe */ _home_HomePage__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Navigation: () => (/* reexport safe */ _layout_Navigation__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   TestimonialsSection: () => (/* reexport safe */ _home_TestimonialsSection__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _layout_Navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./layout/Navigation */ \"(rsc)/./src/components/layout/Navigation.tsx\");\n/* harmony import */ var _layout_Footer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _home_HomePage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./home/<USER>/ \"(rsc)/./src/components/home/<USER>");\n/* harmony import */ var _home_HeroSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./home/<USER>/ \"(rsc)/./src/components/home/<USER>");\n/* harmony import */ var _home_CollectionsGrid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./home/<USER>/ \"(rsc)/./src/components/home/<USER>");\n/* harmony import */ var _home_CatalogSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./home/<USER>/ \"(rsc)/./src/components/home/<USER>");\n/* harmony import */ var _home_FeaturedCollections__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./home/<USER>/ \"(rsc)/./src/components/home/<USER>");\n/* harmony import */ var _home_TestimonialsSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./home/<USER>/ \"(rsc)/./src/components/home/<USER>");\n// Layout Components\n\n\n// Home Page Components\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsb0JBQW9CO0FBQ3dDO0FBQ1I7QUFFcEQsdUJBQXVCO0FBQytCO0FBQ007QUFDUTtBQUNGO0FBQ1U7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVbWVyIEZhcm9vcVxcRGVza3RvcFxcUGF0cmlja3Mgd2ViXFxDYXN0LVN0b25lXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMYXlvdXQgQ29tcG9uZW50c1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBOYXZpZ2F0aW9uIH0gZnJvbSAnLi9sYXlvdXQvTmF2aWdhdGlvbic7XG5leHBvcnQgeyBkZWZhdWx0IGFzIEZvb3RlciB9IGZyb20gJy4vbGF5b3V0L0Zvb3Rlcic7XG5cbi8vIEhvbWUgUGFnZSBDb21wb25lbnRzXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWVQYWdlIH0gZnJvbSAnLi9ob21lL0hvbWVQYWdlJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSGVyb1NlY3Rpb24gfSBmcm9tICcuL2hvbWUvSGVyb1NlY3Rpb24nO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2xsZWN0aW9uc0dyaWQgfSBmcm9tICcuL2hvbWUvQ29sbGVjdGlvbnNHcmlkJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2F0YWxvZ1NlY3Rpb24gfSBmcm9tICcuL2hvbWUvQ2F0YWxvZ1NlY3Rpb24nO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBGZWF0dXJlZENvbGxlY3Rpb25zIH0gZnJvbSAnLi9ob21lL0ZlYXR1cmVkQ29sbGVjdGlvbnMnO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBUZXN0aW1vbmlhbHNTZWN0aW9uIH0gZnJvbSAnLi9ob21lL1Rlc3RpbW9uaWFsc1NlY3Rpb24nO1xuIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJOYXZpZ2F0aW9uIiwiRm9vdGVyIiwiSG9tZVBhZ2UiLCJIZXJvU2VjdGlvbiIsIkNvbGxlY3Rpb25zR3JpZCIsIkNhdGFsb2dTZWN0aW9uIiwiRmVhdHVyZWRDb2xsZWN0aW9ucyIsIlRlc3RpbW9uaWFsc1NlY3Rpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\layout\\Footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\Patricks web\\Cast-Stone\\frontend\\src\\components\\layout\\Navigation.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCatalogSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCollectionsGrid.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CFeaturedCollections.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHomePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CTestimonialsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCatalogSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCollectionsGrid.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CFeaturedCollections.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHomePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CTestimonialsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/home/<USER>/ \"(ssr)/./src/components/home/<USER>"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navigation.tsx */ \"(ssr)/./src/components/layout/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCatalogSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CCollectionsGrid.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CFeaturedCollections.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CHomePage.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Chome%5C%5CTestimonialsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*******************************************************!*\
  !*** ./src/components/home/<USER>
  \*******************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"catalogSection\": \"CatalogSection_catalogSection__xWHSY\",\n\t\"catalogContainer\": \"CatalogSection_catalogContainer__pOYTt\",\n\t\"catalogContent\": \"CatalogSection_catalogContent__Ut8dn\",\n\t\"catalogText\": \"CatalogSection_catalogText__Q2fXT\",\n\t\"catalogBtn\": \"CatalogSection_catalogBtn__M84YU\",\n\t\"catalogImage\": \"CatalogSection_catalogImage__mwGOP\",\n\t\"catalogImg\": \"CatalogSection_catalogImg__Es0x_\"\n};\n\nmodule.exports.__checksum = \"d71f2d817a90\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0NhdGFsb2dTZWN0aW9uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGhvbWVcXENhdGFsb2dTZWN0aW9uLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY2F0YWxvZ1NlY3Rpb25cIjogXCJDYXRhbG9nU2VjdGlvbl9jYXRhbG9nU2VjdGlvbl9feFdIU1lcIixcblx0XCJjYXRhbG9nQ29udGFpbmVyXCI6IFwiQ2F0YWxvZ1NlY3Rpb25fY2F0YWxvZ0NvbnRhaW5lcl9fcE9ZVHRcIixcblx0XCJjYXRhbG9nQ29udGVudFwiOiBcIkNhdGFsb2dTZWN0aW9uX2NhdGFsb2dDb250ZW50X19VdDhkblwiLFxuXHRcImNhdGFsb2dUZXh0XCI6IFwiQ2F0YWxvZ1NlY3Rpb25fY2F0YWxvZ1RleHRfX1EyZlhUXCIsXG5cdFwiY2F0YWxvZ0J0blwiOiBcIkNhdGFsb2dTZWN0aW9uX2NhdGFsb2dCdG5fX004NFlVXCIsXG5cdFwiY2F0YWxvZ0ltYWdlXCI6IFwiQ2F0YWxvZ1NlY3Rpb25fY2F0YWxvZ0ltYWdlX19td0dPUFwiLFxuXHRcImNhdGFsb2dJbWdcIjogXCJDYXRhbG9nU2VjdGlvbl9jYXRhbG9nSW1nX19FczB4X1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJkNzFmMmQ4MTdhOTBcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!************************************************!*\
  !*** ./src/components/home/<USER>
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CatalogSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CatalogSection.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CatalogSection({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogSection)} ${className || ''}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogContent),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogText),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"Explore Our Complete Catalog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Browse through our comprehensive collection of cast stone products. From architectural elements to decorative pieces, find everything you need to transform your space with timeless elegance.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogBtn),\n                                children: \"View Catalog\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogImage),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                        src: \"/images/catalog-preview.jpg\",\n                        alt: \"Cast Stone Catalog\",\n                        width: 600,\n                        height: 400,\n                        className: (_CatalogSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().catalogImg)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CatalogSection.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!********************************************************!*\
  !*** ./src/components/home/<USER>
  \********************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"collectionsSection\": \"CollectionsGrid_collectionsSection__INn5d\",\n\t\"collectionsGrid\": \"CollectionsGrid_collectionsGrid__B6kkM\",\n\t\"collectionCard\": \"CollectionsGrid_collectionCard__qG8Ub\",\n\t\"collectionImage\": \"CollectionsGrid_collectionImage__Jw6QX\",\n\t\"collectionImg\": \"CollectionsGrid_collectionImg__QDbrX\",\n\t\"collectionOverlay\": \"CollectionsGrid_collectionOverlay__jJQyH\",\n\t\"collectionBrand\": \"CollectionsGrid_collectionBrand__16gRy\",\n\t\"collectionSubtitle\": \"CollectionsGrid_collectionSubtitle__6kv2c\",\n\t\"collectionDescription\": \"CollectionsGrid_collectionDescription__yFGMj\",\n\t\"collectionPrice\": \"CollectionsGrid_collectionPrice__OeY5J\",\n\t\"collectionButtons\": \"CollectionsGrid_collectionButtons__Xe7JP\",\n\t\"buildBtn\": \"CollectionsGrid_buildBtn__6ctOy\",\n\t\"allModelsBtn\": \"CollectionsGrid_allModelsBtn__qX2X4\"\n};\n\nmodule.exports.__checksum = \"3125fe8a11bc\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0NvbGxlY3Rpb25zR3JpZC5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHlCQUF5QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVbWVyIEZhcm9vcVxcRGVza3RvcFxcUGF0cmlja3Mgd2ViXFxDYXN0LVN0b25lXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxob21lXFxDb2xsZWN0aW9uc0dyaWQubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJjb2xsZWN0aW9uc1NlY3Rpb25cIjogXCJDb2xsZWN0aW9uc0dyaWRfY29sbGVjdGlvbnNTZWN0aW9uX19JTm41ZFwiLFxuXHRcImNvbGxlY3Rpb25zR3JpZFwiOiBcIkNvbGxlY3Rpb25zR3JpZF9jb2xsZWN0aW9uc0dyaWRfX0I2a2tNXCIsXG5cdFwiY29sbGVjdGlvbkNhcmRcIjogXCJDb2xsZWN0aW9uc0dyaWRfY29sbGVjdGlvbkNhcmRfX3FHOFViXCIsXG5cdFwiY29sbGVjdGlvbkltYWdlXCI6IFwiQ29sbGVjdGlvbnNHcmlkX2NvbGxlY3Rpb25JbWFnZV9fSnc2UVhcIixcblx0XCJjb2xsZWN0aW9uSW1nXCI6IFwiQ29sbGVjdGlvbnNHcmlkX2NvbGxlY3Rpb25JbWdfX1FEYnJYXCIsXG5cdFwiY29sbGVjdGlvbk92ZXJsYXlcIjogXCJDb2xsZWN0aW9uc0dyaWRfY29sbGVjdGlvbk92ZXJsYXlfX2pKUXlIXCIsXG5cdFwiY29sbGVjdGlvbkJyYW5kXCI6IFwiQ29sbGVjdGlvbnNHcmlkX2NvbGxlY3Rpb25CcmFuZF9fMTZnUnlcIixcblx0XCJjb2xsZWN0aW9uU3VidGl0bGVcIjogXCJDb2xsZWN0aW9uc0dyaWRfY29sbGVjdGlvblN1YnRpdGxlX182a3YyY1wiLFxuXHRcImNvbGxlY3Rpb25EZXNjcmlwdGlvblwiOiBcIkNvbGxlY3Rpb25zR3JpZF9jb2xsZWN0aW9uRGVzY3JpcHRpb25fX3lGR01qXCIsXG5cdFwiY29sbGVjdGlvblByaWNlXCI6IFwiQ29sbGVjdGlvbnNHcmlkX2NvbGxlY3Rpb25QcmljZV9fT2VZNUpcIixcblx0XCJjb2xsZWN0aW9uQnV0dG9uc1wiOiBcIkNvbGxlY3Rpb25zR3JpZF9jb2xsZWN0aW9uQnV0dG9uc19fWGU3SlBcIixcblx0XCJidWlsZEJ0blwiOiBcIkNvbGxlY3Rpb25zR3JpZF9idWlsZEJ0bl9fNmN0T3lcIixcblx0XCJhbGxNb2RlbHNCdG5cIjogXCJDb2xsZWN0aW9uc0dyaWRfYWxsTW9kZWxzQnRuX19xWDJYNFwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIzMTI1ZmU4YTExYmNcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CollectionsGrid.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CollectionsGrid({ categories, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionsSection)} ${className || ''}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionsGrid),\n            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionCard),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionImage),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: category.image,\n                                alt: category.title,\n                                fill: true,\n                                className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionImg)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionOverlay),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionBrand),\n                                    children: category.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionSubtitle),\n                                    children: category.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionDescription),\n                                    children: category.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionPrice),\n                                    children: category.price\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionButtons),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().buildBtn),\n                                            children: \"BUILD YOURS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().allModelsBtn),\n                                            children: \"ALL MODELS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, category.id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!************************************************************!*\
  !*** ./src/components/home/<USER>
  \************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"featuredCollections\": \"FeaturedCollections_featuredCollections__RJNQj\",\n\t\"featuredGrid\": \"FeaturedCollections_featuredGrid__EVygU\",\n\t\"featuredCard\": \"FeaturedCollections_featuredCard__r7DIE\",\n\t\"featuredImage\": \"FeaturedCollections_featuredImage__9yjUq\",\n\t\"featuredImg\": \"FeaturedCollections_featuredImg__xJy_R\",\n\t\"featuredContent\": \"FeaturedCollections_featuredContent__tM1Tm\",\n\t\"featuredTitle\": \"FeaturedCollections_featuredTitle__7PpZ0\",\n\t\"featuredSubtitle\": \"FeaturedCollections_featuredSubtitle__qT5hZ\",\n\t\"featuredBtn\": \"FeaturedCollections_featuredBtn___wxeN\"\n};\n\nmodule.exports.__checksum = \"fad24dd1f4a2\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0ZlYXR1cmVkQ29sbGVjdGlvbnMubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGhvbWVcXEZlYXR1cmVkQ29sbGVjdGlvbnMubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJmZWF0dXJlZENvbGxlY3Rpb25zXCI6IFwiRmVhdHVyZWRDb2xsZWN0aW9uc19mZWF0dXJlZENvbGxlY3Rpb25zX19SSk5RalwiLFxuXHRcImZlYXR1cmVkR3JpZFwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRHcmlkX19FVnlnVVwiLFxuXHRcImZlYXR1cmVkQ2FyZFwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRDYXJkX19yN0RJRVwiLFxuXHRcImZlYXR1cmVkSW1hZ2VcIjogXCJGZWF0dXJlZENvbGxlY3Rpb25zX2ZlYXR1cmVkSW1hZ2VfXzl5alVxXCIsXG5cdFwiZmVhdHVyZWRJbWdcIjogXCJGZWF0dXJlZENvbGxlY3Rpb25zX2ZlYXR1cmVkSW1nX194SnlfUlwiLFxuXHRcImZlYXR1cmVkQ29udGVudFwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRDb250ZW50X190TTFUbVwiLFxuXHRcImZlYXR1cmVkVGl0bGVcIjogXCJGZWF0dXJlZENvbGxlY3Rpb25zX2ZlYXR1cmVkVGl0bGVfXzdQcFowXCIsXG5cdFwiZmVhdHVyZWRTdWJ0aXRsZVwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRTdWJ0aXRsZV9fcVQ1aFpcIixcblx0XCJmZWF0dXJlZEJ0blwiOiBcIkZlYXR1cmVkQ29sbGVjdGlvbnNfZmVhdHVyZWRCdG5fX193eGVOXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImZhZDI0ZGQxZjRhMlwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*****************************************************!*\
  !*** ./src/components/home/<USER>
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeaturedCollections)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FeaturedCollections.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction FeaturedCollections({ featuredProducts, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredCollections)} ${className || ''}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredGrid),\n            children: featuredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredCard),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredImage),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: product.image,\n                                alt: product.title,\n                                fill: true,\n                                className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredImg)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredTitle),\n                                    children: product.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredSubtitle),\n                                    children: product.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_FeaturedCollections_module_css__WEBPACK_IMPORTED_MODULE_2___default().featuredBtn),\n                                    children: product.buttonText\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, product.id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\FeaturedCollections.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!****************************************************!*\
  !*** ./src/components/home/<USER>
  \****************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"hero\": \"HeroSection_hero__tVlJy\",\n\t\"videoBackground\": \"HeroSection_videoBackground__1b56U\",\n\t\"heroVideo\": \"HeroSection_heroVideo__wmHr5\",\n\t\"videoOverlay\": \"HeroSection_videoOverlay__GbFoP\",\n\t\"heroContent\": \"HeroSection_heroContent__VYZbs\",\n\t\"heroTitle\": \"HeroSection_heroTitle__nnst_\",\n\t\"highlight\": \"HeroSection_highlight__Wgy18\",\n\t\"heroSubtitle\": \"HeroSection_heroSubtitle__J_PUa\",\n\t\"heroActions\": \"HeroSection_heroActions__KheHS\",\n\t\"primaryBtn\": \"HeroSection_primaryBtn__ajxDg\",\n\t\"secondaryBtn\": \"HeroSection_secondaryBtn__dFomW\"\n};\n\nmodule.exports.__checksum = \"4ecd093e3a8d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0hlcm9TZWN0aW9uLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcaG9tZVxcSGVyb1NlY3Rpb24ubW9kdWxlLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJoZXJvXCI6IFwiSGVyb1NlY3Rpb25faGVyb19fdFZsSnlcIixcblx0XCJ2aWRlb0JhY2tncm91bmRcIjogXCJIZXJvU2VjdGlvbl92aWRlb0JhY2tncm91bmRfXzFiNTZVXCIsXG5cdFwiaGVyb1ZpZGVvXCI6IFwiSGVyb1NlY3Rpb25faGVyb1ZpZGVvX193bUhyNVwiLFxuXHRcInZpZGVvT3ZlcmxheVwiOiBcIkhlcm9TZWN0aW9uX3ZpZGVvT3ZlcmxheV9fR2JGb1BcIixcblx0XCJoZXJvQ29udGVudFwiOiBcIkhlcm9TZWN0aW9uX2hlcm9Db250ZW50X19WWVpic1wiLFxuXHRcImhlcm9UaXRsZVwiOiBcIkhlcm9TZWN0aW9uX2hlcm9UaXRsZV9fbm5zdF9cIixcblx0XCJoaWdobGlnaHRcIjogXCJIZXJvU2VjdGlvbl9oaWdobGlnaHRfX1dneTE4XCIsXG5cdFwiaGVyb1N1YnRpdGxlXCI6IFwiSGVyb1NlY3Rpb25faGVyb1N1YnRpdGxlX19KX1BVYVwiLFxuXHRcImhlcm9BY3Rpb25zXCI6IFwiSGVyb1NlY3Rpb25faGVyb0FjdGlvbnNfX0toZUhTXCIsXG5cdFwicHJpbWFyeUJ0blwiOiBcIkhlcm9TZWN0aW9uX3ByaW1hcnlCdG5fX2FqeERnXCIsXG5cdFwic2Vjb25kYXJ5QnRuXCI6IFwiSGVyb1NlY3Rpb25fc2Vjb25kYXJ5QnRuX19kRm9tV1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCI0ZWNkMDkzZTNhOGRcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*********************************************!*\
  !*** ./src/components/home/<USER>
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./HeroSection.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction HeroSection({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().hero)} ${className || ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().videoBackground),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                        autoPlay: true,\n                        muted: true,\n                        loop: true,\n                        playsInline: true,\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroVideo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                src: \"/herosection.mp4\",\n                                type: \"video/mp4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 11\n                            }, this),\n                            \"Your browser does not support the video tag.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().videoOverlay)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroContent),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroTitle),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().highlight),\n                            children: \"Timeless Elegance in Cast Stone\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroSubtitle),\n                        children: \"Discover our exquisite collection of handcrafted cast stone interiors, fireplaces, and decorative elements that transform spaces into works of art.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().heroActions),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().primaryBtn),\n                                children: \"Explore Collection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_HeroSection_module_css__WEBPACK_IMPORTED_MODULE_1___default().secondaryBtn),\n                                children: \"Watch Our Story\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HeroSection.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"HomePage_container__GVF35\"\n};\n\nmodule.exports.__checksum = \"1134a7422f67\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL0hvbWVQYWdlLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGhvbWVcXEhvbWVQYWdlLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY29udGFpbmVyXCI6IFwiSG9tZVBhZ2VfY29udGFpbmVyX19HVkYzNVwiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCIxMTM0YTc0MjJmNjdcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!******************************************!*\
  !*** ./src/components/home/<USER>
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../index */ \"(ssr)/./src/components/index.ts\");\n/* harmony import */ var _HomePage_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HomePage.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _HomePage_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_HomePage_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction HomePage() {\n    const categories = [\n        {\n            id: 1,\n            title: \"911\",\n            subtitle: \"Architectural\",\n            description: \"The iconic, rear-engine sports car with exceptional performance.\",\n            image: \"/images/architectural-collection.jpg\",\n            price: \"From $1,200\",\n            link: \"/products/architectural\"\n        },\n        {\n            id: 2,\n            title: \"718\",\n            subtitle: \"Designer\",\n            description: \"The mid-engine sports car for two made for pure driving pleasure.\",\n            image: \"/images/fireplace-collection.jpg\",\n            price: \"From $2,500\",\n            link: \"/products/designer\"\n        },\n        {\n            id: 3,\n            title: \"Taycan\",\n            subtitle: \"Limited Edition\",\n            description: \"The soul, electrified. Pure Porsche performance with zero emissions.\",\n            image: \"/images/garden-collection.jpg\",\n            price: \"From $3,800\",\n            link: \"/products/limited-edition\"\n        },\n        {\n            id: 4,\n            title: \"Panamera\",\n            subtitle: \"Sealer Program\",\n            description: \"The luxury sports sedan that combines comfort with performance.\",\n            image: \"/images/hero-cast-stone.jpg\",\n            price: \"From $150\",\n            link: \"/products/sealers\"\n        }\n    ];\n    const featuredProducts = [\n        {\n            id: 1,\n            title: \"DESIGNER'S PICKS\",\n            subtitle: \"A peek inside our designer's shopping cart.\",\n            image: \"/images/fireplace-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/designer-picks\"\n        },\n        {\n            id: 2,\n            title: \"THE CAST STONE SHOP\",\n            subtitle: \"The best of the best, from fireplaces and fountains to architectural elements.\",\n            image: \"/images/garden-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/cast-stone\"\n        },\n        {\n            id: 3,\n            title: \"ARCHITECTURAL ELEMENTS\",\n            subtitle: \"Clean, luxurious, results-driven architectural cast stone pieces.\",\n            image: \"/images/architectural-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/architectural\"\n        },\n        {\n            id: 4,\n            title: \"PREMIUM COLLECTION\",\n            subtitle: \"Classics, reimagined for the modern home.\",\n            image: \"/images/hero-cast-stone.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/premium\"\n        }\n    ];\n    const testimonials = [\n        {\n            id: 1,\n            name: \"Sarah Johnson\",\n            company: \"Johnson Architecture\",\n            text: \"Cast Stone's architectural elements transformed our project. The quality and craftsmanship are unmatched.\",\n            rating: 5\n        },\n        {\n            id: 2,\n            name: \"Michael Chen\",\n            company: \"Elite Homes\",\n            text: \"We've been using Cast Stone products for over 10 years. Their consistency and beauty never disappoint.\",\n            rating: 5\n        },\n        {\n            id: 3,\n            name: \"Emma Rodriguez\",\n            company: \"Rodriguez Design Studio\",\n            text: \"The limited edition pieces add such elegance to our high-end residential projects.\",\n            rating: 5\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_HomePage_module_css__WEBPACK_IMPORTED_MODULE_2___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.Navigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.HeroSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.CollectionsGrid, {\n                categories: categories\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.CatalogSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.FeaturedCollections, {\n                featuredProducts: featuredProducts\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.TestimonialsSection, {\n                testimonials: testimonials\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n        lineNumber: 114,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!************************************************************!*\
  !*** ./src/components/home/<USER>
  \************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"testimonialsSection\": \"TestimonialsSection_testimonialsSection__eFqMD\",\n\t\"sectionHeader\": \"TestimonialsSection_sectionHeader__P_QS4\",\n\t\"testimonialsCarousel\": \"TestimonialsSection_testimonialsCarousel__IrvAW\",\n\t\"testimonialSlide\": \"TestimonialsSection_testimonialSlide__jWab0\",\n\t\"active\": \"TestimonialsSection_active__2TCrK\",\n\t\"testimonialCard\": \"TestimonialsSection_testimonialCard__g3Le5\",\n\t\"stars\": \"TestimonialsSection_stars__sPqUh\",\n\t\"star\": \"TestimonialsSection_star__r9WUB\",\n\t\"testimonialText\": \"TestimonialsSection_testimonialText__rv9eB\",\n\t\"testimonialAuthor\": \"TestimonialsSection_testimonialAuthor__tNCS7\",\n\t\"testimonialControls\": \"TestimonialsSection_testimonialControls__WZrGJ\",\n\t\"testimonialDot\": \"TestimonialsSection_testimonialDot__WabFU\",\n\t\"activeDot\": \"TestimonialsSection_activeDot__NBmoY\"\n};\n\nmodule.exports.__checksum = \"c53e3ec32d5c\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9ob21lL1Rlc3RpbW9uaWFsc1NlY3Rpb24ubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcaG9tZVxcVGVzdGltb25pYWxzU2VjdGlvbi5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInRlc3RpbW9uaWFsc1NlY3Rpb25cIjogXCJUZXN0aW1vbmlhbHNTZWN0aW9uX3Rlc3RpbW9uaWFsc1NlY3Rpb25fX2VGcU1EXCIsXG5cdFwic2VjdGlvbkhlYWRlclwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fc2VjdGlvbkhlYWRlcl9fUF9RUzRcIixcblx0XCJ0ZXN0aW1vbmlhbHNDYXJvdXNlbFwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxzQ2Fyb3VzZWxfX0lydkFXXCIsXG5cdFwidGVzdGltb25pYWxTbGlkZVwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxTbGlkZV9faldhYjBcIixcblx0XCJhY3RpdmVcIjogXCJUZXN0aW1vbmlhbHNTZWN0aW9uX2FjdGl2ZV9fMlRDcktcIixcblx0XCJ0ZXN0aW1vbmlhbENhcmRcIjogXCJUZXN0aW1vbmlhbHNTZWN0aW9uX3Rlc3RpbW9uaWFsQ2FyZF9fZzNMZTVcIixcblx0XCJzdGFyc1wiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fc3RhcnNfX3NQcVVoXCIsXG5cdFwic3RhclwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fc3Rhcl9fcjlXVUJcIixcblx0XCJ0ZXN0aW1vbmlhbFRleHRcIjogXCJUZXN0aW1vbmlhbHNTZWN0aW9uX3Rlc3RpbW9uaWFsVGV4dF9fcnY5ZUJcIixcblx0XCJ0ZXN0aW1vbmlhbEF1dGhvclwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxBdXRob3JfX3ROQ1M3XCIsXG5cdFwidGVzdGltb25pYWxDb250cm9sc1wiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxDb250cm9sc19fV1pyR0pcIixcblx0XCJ0ZXN0aW1vbmlhbERvdFwiOiBcIlRlc3RpbW9uaWFsc1NlY3Rpb25fdGVzdGltb25pYWxEb3RfX1dhYkZVXCIsXG5cdFwiYWN0aXZlRG90XCI6IFwiVGVzdGltb25pYWxzU2VjdGlvbl9hY3RpdmVEb3RfX05CbW9ZXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImM1M2UzZWMzMmQ1Y1wiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/home/<USER>":
/*!*****************************************************!*\
  !*** ./src/components/home/<USER>
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestimonialsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TestimonialsSection.module.css */ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TestimonialsSection({ testimonials, className }) {\n    const [currentTestimonial, setCurrentTestimonial] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestimonialsSection.useEffect\": ()=>{\n            const testimonialInterval = setInterval({\n                \"TestimonialsSection.useEffect.testimonialInterval\": ()=>{\n                    setCurrentTestimonial({\n                        \"TestimonialsSection.useEffect.testimonialInterval\": (prev)=>(prev + 1) % testimonials.length\n                    }[\"TestimonialsSection.useEffect.testimonialInterval\"]);\n                }\n            }[\"TestimonialsSection.useEffect.testimonialInterval\"], 6000);\n            return ({\n                \"TestimonialsSection.useEffect\": ()=>clearInterval(testimonialInterval)\n            })[\"TestimonialsSection.useEffect\"];\n        }\n    }[\"TestimonialsSection.useEffect\"], [\n        testimonials.length\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: `${(_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialsSection)} ${className || ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().sectionHeader),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"What Our Clients Say\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Hear from professionals who trust Cast Stone for their projects\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialsCarousel),\n                children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialSlide)} ${index === currentTestimonial ? (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialCard),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialContent),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().stars),\n                                        children: [\n                                            ...Array(testimonial.rating)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().star),\n                                                children: \"★\"\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialText),\n                                        children: [\n                                            '\"',\n                                            testimonial.text,\n                                            '\"'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialAuthor),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: testimonial.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: testimonial.company\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this)\n                    }, testimonial.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialControls),\n                children: testimonials.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: `${(_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().testimonialDot)} ${index === currentTestimonial ? (_TestimonialsSection_module_css__WEBPACK_IMPORTED_MODULE_2___default().activeDot) : ''}`,\n                        onClick: ()=>setCurrentTestimonial(index)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\TestimonialsSection.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/home/<USER>");

/***/ }),

/***/ "(ssr)/./src/components/index.ts":
/*!*********************************!*\
  !*** ./src/components/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CatalogSection: () => (/* reexport safe */ _home_CatalogSection__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   CollectionsGrid: () => (/* reexport safe */ _home_CollectionsGrid__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   FeaturedCollections: () => (/* reexport safe */ _home_FeaturedCollections__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Footer: () => (/* reexport safe */ _layout_Footer__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   HeroSection: () => (/* reexport safe */ _home_HeroSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   HomePage: () => (/* reexport safe */ _home_HomePage__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Navigation: () => (/* reexport safe */ _layout_Navigation__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   TestimonialsSection: () => (/* reexport safe */ _home_TestimonialsSection__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _layout_Navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./layout/Navigation */ \"(ssr)/./src/components/layout/Navigation.tsx\");\n/* harmony import */ var _layout_Footer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./layout/Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _home_HomePage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_HeroSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_CollectionsGrid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_CatalogSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_FeaturedCollections__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n/* harmony import */ var _home_TestimonialsSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./home/<USER>/ \"(ssr)/./src/components/home/<USER>");\n// Layout Components\n\n\n// Home Page Components\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsb0JBQW9CO0FBQ3dDO0FBQ1I7QUFFcEQsdUJBQXVCO0FBQytCO0FBQ007QUFDUTtBQUNGO0FBQ1U7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVbWVyIEZhcm9vcVxcRGVza3RvcFxcUGF0cmlja3Mgd2ViXFxDYXN0LVN0b25lXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMYXlvdXQgQ29tcG9uZW50c1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBOYXZpZ2F0aW9uIH0gZnJvbSAnLi9sYXlvdXQvTmF2aWdhdGlvbic7XG5leHBvcnQgeyBkZWZhdWx0IGFzIEZvb3RlciB9IGZyb20gJy4vbGF5b3V0L0Zvb3Rlcic7XG5cbi8vIEhvbWUgUGFnZSBDb21wb25lbnRzXG5leHBvcnQgeyBkZWZhdWx0IGFzIEhvbWVQYWdlIH0gZnJvbSAnLi9ob21lL0hvbWVQYWdlJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgSGVyb1NlY3Rpb24gfSBmcm9tICcuL2hvbWUvSGVyb1NlY3Rpb24nO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBDb2xsZWN0aW9uc0dyaWQgfSBmcm9tICcuL2hvbWUvQ29sbGVjdGlvbnNHcmlkJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2F0YWxvZ1NlY3Rpb24gfSBmcm9tICcuL2hvbWUvQ2F0YWxvZ1NlY3Rpb24nO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBGZWF0dXJlZENvbGxlY3Rpb25zIH0gZnJvbSAnLi9ob21lL0ZlYXR1cmVkQ29sbGVjdGlvbnMnO1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBUZXN0aW1vbmlhbHNTZWN0aW9uIH0gZnJvbSAnLi9ob21lL1Rlc3RpbW9uaWFsc1NlY3Rpb24nO1xuIl0sIm5hbWVzIjpbImRlZmF1bHQiLCJOYXZpZ2F0aW9uIiwiRm9vdGVyIiwiSG9tZVBhZ2UiLCJIZXJvU2VjdGlvbiIsIkNvbGxlY3Rpb25zR3JpZCIsIkNhdGFsb2dTZWN0aW9uIiwiRmVhdHVyZWRDb2xsZWN0aW9ucyIsIlRlc3RpbW9uaWFsc1NlY3Rpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/index.ts\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.module.css":
/*!*************************************************!*\
  !*** ./src/components/layout/Footer.module.css ***!
  \*************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"footer\": \"Footer_footer__eNA9m\",\n\t\"footerContent\": \"Footer_footerContent__7IEzx\",\n\t\"footerSection\": \"Footer_footerSection__QRm_X\",\n\t\"footerBottom\": \"Footer_footerBottom__BDIjN\"\n};\n\nmodule.exports.__checksum = \"f4aa87e8bd8d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcRm9vdGVyLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiZm9vdGVyXCI6IFwiRm9vdGVyX2Zvb3Rlcl9fZU5BOW1cIixcblx0XCJmb290ZXJDb250ZW50XCI6IFwiRm9vdGVyX2Zvb3RlckNvbnRlbnRfXzdJRXp4XCIsXG5cdFwiZm9vdGVyU2VjdGlvblwiOiBcIkZvb3Rlcl9mb290ZXJTZWN0aW9uX19RUm1fWFwiLFxuXHRcImZvb3RlckJvdHRvbVwiOiBcIkZvb3Rlcl9mb290ZXJCb3R0b21fX0JESWpOXCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImY0YWE4N2U4YmQ4ZFwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Footer_module_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Footer.module.css */ \"(ssr)/./src/components/layout/Footer.module.css\");\n/* harmony import */ var _Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_Footer_module_css__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Footer({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: `${(_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footer)} ${className || ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerContent),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Cast Stone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Creating timeless beauty with handcrafted cast stone elements for over 25 years.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Company\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/contact\",\n                                            children: \"Contact Us\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 20,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/about\",\n                                            children: \"Our Story\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/retail-locator\",\n                                            children: \"Retail Locator\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 22,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/wholesale-signup\",\n                                            children: \"Wholesale Sign-up\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/products/architectural\",\n                                            children: \"Architectural Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/products/designer\",\n                                            children: \"Designer Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/products/limited-edition\",\n                                            children: \"Limited Edition\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/products/sealers\",\n                                            children: \"Cast Stone Sealers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Discover\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/catalog\",\n                                            children: \"Catalog\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/collections\",\n                                            children: \"Collections\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/projects\",\n                                            children: \"Completed Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/videos\",\n                                            children: \"Videos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/faqs\",\n                                            children: \"FAQs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"Contact Info\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    \"123 Artisan Way\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 29\n                                    }, this),\n                                    \"Craftsman City, CC 12345\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Phone: (*************\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Email: <EMAIL>\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_Footer_module_css__WEBPACK_IMPORTED_MODULE_1___default().footerBottom),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"\\xa9 2024 Cast Stone Interiors. All rights reserved.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navigation.module.css":
/*!*****************************************************!*\
  !*** ./src/components/layout/Navigation.module.css ***!
  \*****************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"navigation\": \"Navigation_navigation__Z9RLH\",\n\t\"navContainer\": \"Navigation_navContainer__B3qJN\",\n\t\"logo\": \"Navigation_logo__hOIJ2\",\n\t\"navMenu\": \"Navigation_navMenu__KOB3_\",\n\t\"dropdown\": \"Navigation_dropdown__yKMbf\",\n\t\"dropdownToggle\": \"Navigation_dropdownToggle__RrJpI\",\n\t\"dropdownMenu\": \"Navigation_dropdownMenu__ZGhNW\",\n\t\"mobileMenuToggle\": \"Navigation_mobileMenuToggle__68btg\",\n\t\"hamburgerLine\": \"Navigation_hamburgerLine__4Hjy7\",\n\t\"active\": \"Navigation_active__Kj1W7\",\n\t\"mobileMenu\": \"Navigation_mobileMenu__rCiva\",\n\t\"mobileNavMenu\": \"Navigation_mobileNavMenu__r9Kw5\",\n\t\"mobileDropdownToggle\": \"Navigation_mobileDropdownToggle__wfkhE\",\n\t\"mobileDropdownMenu\": \"Navigation_mobileDropdownMenu__g5Mxu\"\n};\n\nmodule.exports.__checksum = \"af56dad342ba\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvTmF2aWdhdGlvbi5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEseUJBQXlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcTmF2aWdhdGlvbi5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIm5hdmlnYXRpb25cIjogXCJOYXZpZ2F0aW9uX25hdmlnYXRpb25fX1o5UkxIXCIsXG5cdFwibmF2Q29udGFpbmVyXCI6IFwiTmF2aWdhdGlvbl9uYXZDb250YWluZXJfX0IzcUpOXCIsXG5cdFwibG9nb1wiOiBcIk5hdmlnYXRpb25fbG9nb19faE9JSjJcIixcblx0XCJuYXZNZW51XCI6IFwiTmF2aWdhdGlvbl9uYXZNZW51X19LT0IzX1wiLFxuXHRcImRyb3Bkb3duXCI6IFwiTmF2aWdhdGlvbl9kcm9wZG93bl9feUtNYmZcIixcblx0XCJkcm9wZG93blRvZ2dsZVwiOiBcIk5hdmlnYXRpb25fZHJvcGRvd25Ub2dnbGVfX1JySnBJXCIsXG5cdFwiZHJvcGRvd25NZW51XCI6IFwiTmF2aWdhdGlvbl9kcm9wZG93bk1lbnVfX1pHaE5XXCIsXG5cdFwibW9iaWxlTWVudVRvZ2dsZVwiOiBcIk5hdmlnYXRpb25fbW9iaWxlTWVudVRvZ2dsZV9fNjhidGdcIixcblx0XCJoYW1idXJnZXJMaW5lXCI6IFwiTmF2aWdhdGlvbl9oYW1idXJnZXJMaW5lX180SGp5N1wiLFxuXHRcImFjdGl2ZVwiOiBcIk5hdmlnYXRpb25fYWN0aXZlX19LajFXN1wiLFxuXHRcIm1vYmlsZU1lbnVcIjogXCJOYXZpZ2F0aW9uX21vYmlsZU1lbnVfX3JDaXZhXCIsXG5cdFwibW9iaWxlTmF2TWVudVwiOiBcIk5hdmlnYXRpb25fbW9iaWxlTmF2TWVudV9fcjlLdzVcIixcblx0XCJtb2JpbGVEcm9wZG93blRvZ2dsZVwiOiBcIk5hdmlnYXRpb25fbW9iaWxlRHJvcGRvd25Ub2dnbGVfX3dma2hFXCIsXG5cdFwibW9iaWxlRHJvcGRvd25NZW51XCI6IFwiTmF2aWdhdGlvbl9tb2JpbGVEcm9wZG93bk1lbnVfX2c1TXh1XCJcbn07XG5cbm1vZHVsZS5leHBvcnRzLl9fY2hlY2tzdW0gPSBcImFmNTZkYWQzNDJiYVwiXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navigation.module.css\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Navigation_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navigation.module.css */ \"(ssr)/./src/components/layout/Navigation.module.css\");\n/* harmony import */ var _Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Navigation({ className }) {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileDropdowns, setMobileDropdowns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        company: false,\n        products: false,\n        discover: false\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navigation)} ${className || ''}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().logo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: \"Cast Stone\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Interiors & Decorations\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().navMenu),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownToggle),\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownMenu),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/contact\",\n                                                    children: \"Contact Us\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 31,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/about\",\n                                                    children: \"Our Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/retail-locator\",\n                                                    children: \"Retail Locator\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 33,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/wholesale-signup\",\n                                                    children: \"Wholesale Sign-up\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 34,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownToggle),\n                                        children: \"Products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownMenu),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/architectural\",\n                                                    children: \"Architectural Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/designer\",\n                                                    children: \"Designer Products\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/limited-edition\",\n                                                    children: \"Limited Edition Designs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/products/sealers\",\n                                                    children: \"Cast Stone Sealers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/collections\",\n                                    children: \"Collections\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/projects\",\n                                    children: \"Completed Projects\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownToggle),\n                                        children: \"Discover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().dropdownMenu),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/catalog\",\n                                                    children: \"Catalog\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/finishes\",\n                                                    children: \"Finishes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/videos\",\n                                                    children: \"Videos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/technical\",\n                                                    children: \"Technical Info\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"/faqs\",\n                                                    children: \"FAQs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileMenuToggle)} ${mobileMenuOpen ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().hamburgerLine)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileMenu)} ${mobileMenuOpen ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileNavMenu),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownToggle)} ${mobileDropdowns.company ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                    onClick: ()=>setMobileDropdowns((prev)=>({\n                                                ...prev,\n                                                company: !prev.company\n                                            })),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Company\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownMenu)} ${mobileDropdowns.company ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/contact\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Contact Us\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/about\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Our Story\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/retail-locator\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Retail Locator\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/wholesale-signup\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Wholesale Sign-up\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownToggle)} ${mobileDropdowns.products ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                    onClick: ()=>setMobileDropdowns((prev)=>({\n                                                ...prev,\n                                                products: !prev.products\n                                            })),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownMenu)} ${mobileDropdowns.products ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/products/architectural\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Architectural Products\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/products/designer\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Designer Products\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/products/limited-edition\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Limited Edition Designs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/products/sealers\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Cast Stone Sealers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/collections\",\n                                onClick: ()=>setMobileMenuOpen(false),\n                                children: \"Collections\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/projects\",\n                                onClick: ()=>setMobileMenuOpen(false),\n                                children: \"Completed Projects\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownToggle)} ${mobileDropdowns.discover ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                    onClick: ()=>setMobileDropdowns((prev)=>({\n                                                ...prev,\n                                                discover: !prev.discover\n                                            })),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Discover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: `${(_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().mobileDropdownMenu)} ${mobileDropdowns.discover ? (_Navigation_module_css__WEBPACK_IMPORTED_MODULE_2___default().active) : ''}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/catalog\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Catalog\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/finishes\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Finishes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/videos\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Videos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/technical\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"Technical Info\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/faqs\",\n                                                onClick: ()=>setMobileMenuOpen(false),\n                                                children: \"FAQs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvTmF2aWdhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFaUM7QUFDWTtBQU05QixTQUFTRSxXQUFXLEVBQUVDLFNBQVMsRUFBbUI7SUFDL0QsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHTCwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNNLGlCQUFpQkMsbUJBQW1CLEdBQUdQLCtDQUFRQSxDQUFDO1FBQ3JEUSxTQUFTO1FBQ1RDLFVBQVU7UUFDVkMsVUFBVTtJQUNaO0lBRUEscUJBQ0UsOERBQUNDO1FBQUlSLFdBQVcsR0FBR0YsMEVBQWlCLENBQUMsQ0FBQyxFQUFFRSxhQUFhLElBQUk7OzBCQUN2RCw4REFBQ1U7Z0JBQUlWLFdBQVdGLDRFQUFtQjs7a0NBQ2pDLDhEQUFDWTt3QkFBSVYsV0FBV0Ysb0VBQVc7OzBDQUN6Qiw4REFBQ2U7MENBQUc7Ozs7OzswQ0FDSiw4REFBQ0M7MENBQUs7Ozs7Ozs7Ozs7OztrQ0FJUiw4REFBQ0M7d0JBQUdmLFdBQVdGLHVFQUFjOzswQ0FDM0IsOERBQUNtQjtnQ0FBR2pCLFdBQVdGLHdFQUFlOztrREFDNUIsOERBQUNxQjt3Q0FBRUMsTUFBSzt3Q0FBSXBCLFdBQVdGLDhFQUFxQjtrREFBRTs7Ozs7O2tEQUM5Qyw4REFBQ2lCO3dDQUFHZixXQUFXRiw0RUFBbUI7OzBEQUNoQyw4REFBQ21COzBEQUFHLDRFQUFDRTtvREFBRUMsTUFBSzs4REFBVzs7Ozs7Ozs7Ozs7MERBQ3ZCLDhEQUFDSDswREFBRyw0RUFBQ0U7b0RBQUVDLE1BQUs7OERBQVM7Ozs7Ozs7Ozs7OzBEQUNyQiw4REFBQ0g7MERBQUcsNEVBQUNFO29EQUFFQyxNQUFLOzhEQUFrQjs7Ozs7Ozs7Ozs7MERBQzlCLDhEQUFDSDswREFBRyw0RUFBQ0U7b0RBQUVDLE1BQUs7OERBQW9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHcEMsOERBQUNIO2dDQUFHakIsV0FBV0Ysd0VBQWU7O2tEQUM1Qiw4REFBQ3FCO3dDQUFFQyxNQUFLO3dDQUFJcEIsV0FBV0YsOEVBQXFCO2tEQUFFOzs7Ozs7a0RBQzlDLDhEQUFDaUI7d0NBQUdmLFdBQVdGLDRFQUFtQjs7MERBQ2hDLDhEQUFDbUI7MERBQUcsNEVBQUNFO29EQUFFQyxNQUFLOzhEQUEwQjs7Ozs7Ozs7Ozs7MERBQ3RDLDhEQUFDSDswREFBRyw0RUFBQ0U7b0RBQUVDLE1BQUs7OERBQXFCOzs7Ozs7Ozs7OzswREFDakMsOERBQUNIOzBEQUFHLDRFQUFDRTtvREFBRUMsTUFBSzs4REFBNEI7Ozs7Ozs7Ozs7OzBEQUN4Qyw4REFBQ0g7MERBQUcsNEVBQUNFO29EQUFFQyxNQUFLOzhEQUFvQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBR3BDLDhEQUFDSDswQ0FBRyw0RUFBQ0U7b0NBQUVDLE1BQUs7OENBQWU7Ozs7Ozs7Ozs7OzBDQUMzQiw4REFBQ0g7MENBQUcsNEVBQUNFO29DQUFFQyxNQUFLOzhDQUFZOzs7Ozs7Ozs7OzswQ0FDeEIsOERBQUNIO2dDQUFHakIsV0FBV0Ysd0VBQWU7O2tEQUM1Qiw4REFBQ3FCO3dDQUFFQyxNQUFLO3dDQUFJcEIsV0FBV0YsOEVBQXFCO2tEQUFFOzs7Ozs7a0RBQzlDLDhEQUFDaUI7d0NBQUdmLFdBQVdGLDRFQUFtQjs7MERBQ2hDLDhEQUFDbUI7MERBQUcsNEVBQUNFO29EQUFFQyxNQUFLOzhEQUFXOzs7Ozs7Ozs7OzswREFDdkIsOERBQUNIOzBEQUFHLDRFQUFDRTtvREFBRUMsTUFBSzs4REFBWTs7Ozs7Ozs7Ozs7MERBQ3hCLDhEQUFDSDswREFBRyw0RUFBQ0U7b0RBQUVDLE1BQUs7OERBQVU7Ozs7Ozs7Ozs7OzBEQUN0Qiw4REFBQ0g7MERBQUcsNEVBQUNFO29EQUFFQyxNQUFLOzhEQUFhOzs7Ozs7Ozs7OzswREFDekIsOERBQUNIOzBEQUFHLDRFQUFDRTtvREFBRUMsTUFBSzs4REFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBTTFCLDhEQUFDVjt3QkFDQ1YsV0FBVyxHQUFHRixnRkFBdUIsQ0FBQyxDQUFDLEVBQUVHLGlCQUFpQkgsc0VBQWEsR0FBRyxJQUFJO3dCQUM5RTJCLFNBQVMsSUFBTXZCLGtCQUFrQixDQUFDRDs7MENBRWxDLDhEQUFDUztnQ0FBSVYsV0FBV0YsNkVBQW9COzs7Ozs7MENBQ3BDLDhEQUFDWTtnQ0FBSVYsV0FBV0YsNkVBQW9COzs7Ozs7MENBQ3BDLDhEQUFDWTtnQ0FBSVYsV0FBV0YsNkVBQW9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS3hDLDhEQUFDWTtnQkFBSVYsV0FBVyxHQUFHRiwwRUFBaUIsQ0FBQyxDQUFDLEVBQUVHLGlCQUFpQkgsc0VBQWEsR0FBRyxJQUFJOzBCQUMzRSw0RUFBQ2lCO29CQUFHZixXQUFXRiw2RUFBb0I7O3NDQUNqQyw4REFBQ21COzs4Q0FDQyw4REFBQ1A7b0NBQ0NWLFdBQVcsR0FBR0Ysb0ZBQTJCLENBQUMsQ0FBQyxFQUFFSyxnQkFBZ0JFLE9BQU8sR0FBR1Asc0VBQWEsR0FBRyxJQUFJO29DQUMzRjJCLFNBQVMsSUFBTXJCLG1CQUFtQjBCLENBQUFBLE9BQVM7Z0RBQUMsR0FBR0EsSUFBSTtnREFBRXpCLFNBQVMsQ0FBQ3lCLEtBQUt6QixPQUFPOzRDQUFBOzhDQUUzRSw0RUFBQ1M7a0RBQUs7Ozs7Ozs7Ozs7OzhDQUVSLDhEQUFDQztvQ0FBR2YsV0FBVyxHQUFHRixrRkFBeUIsQ0FBQyxDQUFDLEVBQUVLLGdCQUFnQkUsT0FBTyxHQUFHUCxzRUFBYSxHQUFHLElBQUk7O3NEQUMzRiw4REFBQ21CO3NEQUFHLDRFQUFDRTtnREFBRUMsTUFBSztnREFBV0ssU0FBUyxJQUFNdkIsa0JBQWtCOzBEQUFROzs7Ozs7Ozs7OztzREFDaEUsOERBQUNlO3NEQUFHLDRFQUFDRTtnREFBRUMsTUFBSztnREFBU0ssU0FBUyxJQUFNdkIsa0JBQWtCOzBEQUFROzs7Ozs7Ozs7OztzREFDOUQsOERBQUNlO3NEQUFHLDRFQUFDRTtnREFBRUMsTUFBSztnREFBa0JLLFNBQVMsSUFBTXZCLGtCQUFrQjswREFBUTs7Ozs7Ozs7Ozs7c0RBQ3ZFLDhEQUFDZTtzREFBRyw0RUFBQ0U7Z0RBQUVDLE1BQUs7Z0RBQW9CSyxTQUFTLElBQU12QixrQkFBa0I7MERBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUc3RSw4REFBQ2U7OzhDQUNDLDhEQUFDUDtvQ0FDQ1YsV0FBVyxHQUFHRixvRkFBMkIsQ0FBQyxDQUFDLEVBQUVLLGdCQUFnQkcsUUFBUSxHQUFHUixzRUFBYSxHQUFHLElBQUk7b0NBQzVGMkIsU0FBUyxJQUFNckIsbUJBQW1CMEIsQ0FBQUEsT0FBUztnREFBQyxHQUFHQSxJQUFJO2dEQUFFeEIsVUFBVSxDQUFDd0IsS0FBS3hCLFFBQVE7NENBQUE7OENBRTdFLDRFQUFDUTtrREFBSzs7Ozs7Ozs7Ozs7OENBRVIsOERBQUNDO29DQUFHZixXQUFXLEdBQUdGLGtGQUF5QixDQUFDLENBQUMsRUFBRUssZ0JBQWdCRyxRQUFRLEdBQUdSLHNFQUFhLEdBQUcsSUFBSTs7c0RBQzVGLDhEQUFDbUI7c0RBQUcsNEVBQUNFO2dEQUFFQyxNQUFLO2dEQUEwQkssU0FBUyxJQUFNdkIsa0JBQWtCOzBEQUFROzs7Ozs7Ozs7OztzREFDL0UsOERBQUNlO3NEQUFHLDRFQUFDRTtnREFBRUMsTUFBSztnREFBcUJLLFNBQVMsSUFBTXZCLGtCQUFrQjswREFBUTs7Ozs7Ozs7Ozs7c0RBQzFFLDhEQUFDZTtzREFBRyw0RUFBQ0U7Z0RBQUVDLE1BQUs7Z0RBQTRCSyxTQUFTLElBQU12QixrQkFBa0I7MERBQVE7Ozs7Ozs7Ozs7O3NEQUNqRiw4REFBQ2U7c0RBQUcsNEVBQUNFO2dEQUFFQyxNQUFLO2dEQUFvQkssU0FBUyxJQUFNdkIsa0JBQWtCOzBEQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHN0UsOERBQUNlO3NDQUNDLDRFQUFDRTtnQ0FBRUMsTUFBSztnQ0FBZUssU0FBUyxJQUFNdkIsa0JBQWtCOzBDQUFROzs7Ozs7Ozs7OztzQ0FFbEUsOERBQUNlO3NDQUNDLDRFQUFDRTtnQ0FBRUMsTUFBSztnQ0FBWUssU0FBUyxJQUFNdkIsa0JBQWtCOzBDQUFROzs7Ozs7Ozs7OztzQ0FFL0QsOERBQUNlOzs4Q0FDQyw4REFBQ1A7b0NBQ0NWLFdBQVcsR0FBR0Ysb0ZBQTJCLENBQUMsQ0FBQyxFQUFFSyxnQkFBZ0JJLFFBQVEsR0FBR1Qsc0VBQWEsR0FBRyxJQUFJO29DQUM1RjJCLFNBQVMsSUFBTXJCLG1CQUFtQjBCLENBQUFBLE9BQVM7Z0RBQUMsR0FBR0EsSUFBSTtnREFBRXZCLFVBQVUsQ0FBQ3VCLEtBQUt2QixRQUFROzRDQUFBOzhDQUU3RSw0RUFBQ087a0RBQUs7Ozs7Ozs7Ozs7OzhDQUVSLDhEQUFDQztvQ0FBR2YsV0FBVyxHQUFHRixrRkFBeUIsQ0FBQyxDQUFDLEVBQUVLLGdCQUFnQkksUUFBUSxHQUFHVCxzRUFBYSxHQUFHLElBQUk7O3NEQUM1Riw4REFBQ21CO3NEQUFHLDRFQUFDRTtnREFBRUMsTUFBSztnREFBV0ssU0FBUyxJQUFNdkIsa0JBQWtCOzBEQUFROzs7Ozs7Ozs7OztzREFDaEUsOERBQUNlO3NEQUFHLDRFQUFDRTtnREFBRUMsTUFBSztnREFBWUssU0FBUyxJQUFNdkIsa0JBQWtCOzBEQUFROzs7Ozs7Ozs7OztzREFDakUsOERBQUNlO3NEQUFHLDRFQUFDRTtnREFBRUMsTUFBSztnREFBVUssU0FBUyxJQUFNdkIsa0JBQWtCOzBEQUFROzs7Ozs7Ozs7OztzREFDL0QsOERBQUNlO3NEQUFHLDRFQUFDRTtnREFBRUMsTUFBSztnREFBYUssU0FBUyxJQUFNdkIsa0JBQWtCOzBEQUFROzs7Ozs7Ozs7OztzREFDbEUsOERBQUNlO3NEQUFHLDRFQUFDRTtnREFBRUMsTUFBSztnREFBUUssU0FBUyxJQUFNdkIsa0JBQWtCOzBEQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzNFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXENhc3QtU3RvbmVcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcTmF2aWdhdGlvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBzdHlsZXMgZnJvbSAnLi9OYXZpZ2F0aW9uLm1vZHVsZS5jc3MnO1xuXG5pbnRlcmZhY2UgTmF2aWdhdGlvblByb3BzIHtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOYXZpZ2F0aW9uKHsgY2xhc3NOYW1lIH06IE5hdmlnYXRpb25Qcm9wcykge1xuICBjb25zdCBbbW9iaWxlTWVudU9wZW4sIHNldE1vYmlsZU1lbnVPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW21vYmlsZURyb3Bkb3ducywgc2V0TW9iaWxlRHJvcGRvd25zXSA9IHVzZVN0YXRlKHtcbiAgICBjb21wYW55OiBmYWxzZSxcbiAgICBwcm9kdWN0czogZmFsc2UsXG4gICAgZGlzY292ZXI6IGZhbHNlXG4gIH0pO1xuXG4gIHJldHVybiAoXG4gICAgPG5hdiBjbGFzc05hbWU9e2Ake3N0eWxlcy5uYXZpZ2F0aW9ufSAke2NsYXNzTmFtZSB8fCAnJ31gfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubmF2Q29udGFpbmVyfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5sb2dvfT5cbiAgICAgICAgICA8aDE+Q2FzdCBTdG9uZTwvaDE+XG4gICAgICAgICAgPHNwYW4+SW50ZXJpb3JzICYgRGVjb3JhdGlvbnM8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBEZXNrdG9wIE1lbnUgKi99XG4gICAgICAgIDx1bCBjbGFzc05hbWU9e3N0eWxlcy5uYXZNZW51fT5cbiAgICAgICAgICA8bGkgY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd259PlxuICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9e3N0eWxlcy5kcm9wZG93blRvZ2dsZX0+Q29tcGFueTwvYT5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9e3N0eWxlcy5kcm9wZG93bk1lbnV9PlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9jb250YWN0XCI+Q29udGFjdCBVczwvYT48L2xpPlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9hYm91dFwiPk91ciBTdG9yeTwvYT48L2xpPlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9yZXRhaWwtbG9jYXRvclwiPlJldGFpbCBMb2NhdG9yPC9hPjwvbGk+XG4gICAgICAgICAgICAgIDxsaT48YSBocmVmPVwiL3dob2xlc2FsZS1zaWdudXBcIj5XaG9sZXNhbGUgU2lnbi11cDwvYT48L2xpPlxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2xpPlxuICAgICAgICAgIDxsaSBjbGFzc05hbWU9e3N0eWxlcy5kcm9wZG93bn0+XG4gICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT17c3R5bGVzLmRyb3Bkb3duVG9nZ2xlfT5Qcm9kdWN0czwvYT5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9e3N0eWxlcy5kcm9wZG93bk1lbnV9PlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9wcm9kdWN0cy9hcmNoaXRlY3R1cmFsXCI+QXJjaGl0ZWN0dXJhbCBQcm9kdWN0czwvYT48L2xpPlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9wcm9kdWN0cy9kZXNpZ25lclwiPkRlc2lnbmVyIFByb2R1Y3RzPC9hPjwvbGk+XG4gICAgICAgICAgICAgIDxsaT48YSBocmVmPVwiL3Byb2R1Y3RzL2xpbWl0ZWQtZWRpdGlvblwiPkxpbWl0ZWQgRWRpdGlvbiBEZXNpZ25zPC9hPjwvbGk+XG4gICAgICAgICAgICAgIDxsaT48YSBocmVmPVwiL3Byb2R1Y3RzL3NlYWxlcnNcIj5DYXN0IFN0b25lIFNlYWxlcnM8L2E+PC9saT5cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPC9saT5cbiAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9jb2xsZWN0aW9uc1wiPkNvbGxlY3Rpb25zPC9hPjwvbGk+XG4gICAgICAgICAgPGxpPjxhIGhyZWY9XCIvcHJvamVjdHNcIj5Db21wbGV0ZWQgUHJvamVjdHM8L2E+PC9saT5cbiAgICAgICAgICA8bGkgY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd259PlxuICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9e3N0eWxlcy5kcm9wZG93blRvZ2dsZX0+RGlzY292ZXI8L2E+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd25NZW51fT5cbiAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvY2F0YWxvZ1wiPkNhdGFsb2c8L2E+PC9saT5cbiAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvZmluaXNoZXNcIj5GaW5pc2hlczwvYT48L2xpPlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi92aWRlb3NcIj5WaWRlb3M8L2E+PC9saT5cbiAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvdGVjaG5pY2FsXCI+VGVjaG5pY2FsIEluZm88L2E+PC9saT5cbiAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvZmFxc1wiPkZBUXM8L2E+PC9saT5cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPC9saT5cbiAgICAgICAgPC91bD5cblxuICAgICAgICB7LyogTW9iaWxlIE1lbnUgVG9nZ2xlICovfVxuICAgICAgICA8ZGl2XG4gICAgICAgICAgY2xhc3NOYW1lPXtgJHtzdHlsZXMubW9iaWxlTWVudVRvZ2dsZX0gJHttb2JpbGVNZW51T3BlbiA/IHN0eWxlcy5hY3RpdmUgOiAnJ31gfVxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKCFtb2JpbGVNZW51T3Blbil9XG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmhhbWJ1cmdlckxpbmV9PjwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuaGFtYnVyZ2VyTGluZX0+PC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5oYW1idXJnZXJMaW5lfT48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIE1vYmlsZSBNZW51ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2Ake3N0eWxlcy5tb2JpbGVNZW51fSAke21vYmlsZU1lbnVPcGVuID8gc3R5bGVzLmFjdGl2ZSA6ICcnfWB9PlxuICAgICAgICA8dWwgY2xhc3NOYW1lPXtzdHlsZXMubW9iaWxlTmF2TWVudX0+XG4gICAgICAgICAgPGxpPlxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy5tb2JpbGVEcm9wZG93blRvZ2dsZX0gJHttb2JpbGVEcm9wZG93bnMuY29tcGFueSA/IHN0eWxlcy5hY3RpdmUgOiAnJ31gfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVEcm9wZG93bnMocHJldiA9PiAoey4uLnByZXYsIGNvbXBhbnk6ICFwcmV2LmNvbXBhbnl9KSl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxzcGFuPkNvbXBhbnk8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9e2Ake3N0eWxlcy5tb2JpbGVEcm9wZG93bk1lbnV9ICR7bW9iaWxlRHJvcGRvd25zLmNvbXBhbnkgPyBzdHlsZXMuYWN0aXZlIDogJyd9YH0+XG4gICAgICAgICAgICAgIDxsaT48YSBocmVmPVwiL2NvbnRhY3RcIiBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9PkNvbnRhY3QgVXM8L2E+PC9saT5cbiAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvYWJvdXRcIiBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9Pk91ciBTdG9yeTwvYT48L2xpPlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9yZXRhaWwtbG9jYXRvclwiIG9uQ2xpY2s9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKX0+UmV0YWlsIExvY2F0b3I8L2E+PC9saT5cbiAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvd2hvbGVzYWxlLXNpZ251cFwiIG9uQ2xpY2s9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKX0+V2hvbGVzYWxlIFNpZ24tdXA8L2E+PC9saT5cbiAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgPC9saT5cbiAgICAgICAgICA8bGk+XG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YCR7c3R5bGVzLm1vYmlsZURyb3Bkb3duVG9nZ2xlfSAke21vYmlsZURyb3Bkb3ducy5wcm9kdWN0cyA/IHN0eWxlcy5hY3RpdmUgOiAnJ31gfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVEcm9wZG93bnMocHJldiA9PiAoey4uLnByZXYsIHByb2R1Y3RzOiAhcHJldi5wcm9kdWN0c30pKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHNwYW4+UHJvZHVjdHM8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9e2Ake3N0eWxlcy5tb2JpbGVEcm9wZG93bk1lbnV9ICR7bW9iaWxlRHJvcGRvd25zLnByb2R1Y3RzID8gc3R5bGVzLmFjdGl2ZSA6ICcnfWB9PlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9wcm9kdWN0cy9hcmNoaXRlY3R1cmFsXCIgb25DbGljaz17KCkgPT4gc2V0TW9iaWxlTWVudU9wZW4oZmFsc2UpfT5BcmNoaXRlY3R1cmFsIFByb2R1Y3RzPC9hPjwvbGk+XG4gICAgICAgICAgICAgIDxsaT48YSBocmVmPVwiL3Byb2R1Y3RzL2Rlc2lnbmVyXCIgb25DbGljaz17KCkgPT4gc2V0TW9iaWxlTWVudU9wZW4oZmFsc2UpfT5EZXNpZ25lciBQcm9kdWN0czwvYT48L2xpPlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9wcm9kdWN0cy9saW1pdGVkLWVkaXRpb25cIiBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9PkxpbWl0ZWQgRWRpdGlvbiBEZXNpZ25zPC9hPjwvbGk+XG4gICAgICAgICAgICAgIDxsaT48YSBocmVmPVwiL3Byb2R1Y3RzL3NlYWxlcnNcIiBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9PkNhc3QgU3RvbmUgU2VhbGVyczwvYT48L2xpPlxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L2xpPlxuICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgIDxhIGhyZWY9XCIvY29sbGVjdGlvbnNcIiBvbkNsaWNrPXsoKSA9PiBzZXRNb2JpbGVNZW51T3BlbihmYWxzZSl9PkNvbGxlY3Rpb25zPC9hPlxuICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgPGxpPlxuICAgICAgICAgICAgPGEgaHJlZj1cIi9wcm9qZWN0c1wiIG9uQ2xpY2s9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKX0+Q29tcGxldGVkIFByb2plY3RzPC9hPlxuICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgPGxpPlxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy5tb2JpbGVEcm9wZG93blRvZ2dsZX0gJHttb2JpbGVEcm9wZG93bnMuZGlzY292ZXIgPyBzdHlsZXMuYWN0aXZlIDogJyd9YH1cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0TW9iaWxlRHJvcGRvd25zKHByZXYgPT4gKHsuLi5wcmV2LCBkaXNjb3ZlcjogIXByZXYuZGlzY292ZXJ9KSl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxzcGFuPkRpc2NvdmVyPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPXtgJHtzdHlsZXMubW9iaWxlRHJvcGRvd25NZW51fSAke21vYmlsZURyb3Bkb3ducy5kaXNjb3ZlciA/IHN0eWxlcy5hY3RpdmUgOiAnJ31gfT5cbiAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvY2F0YWxvZ1wiIG9uQ2xpY2s9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKX0+Q2F0YWxvZzwvYT48L2xpPlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9maW5pc2hlc1wiIG9uQ2xpY2s9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKX0+RmluaXNoZXM8L2E+PC9saT5cbiAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvdmlkZW9zXCIgb25DbGljaz17KCkgPT4gc2V0TW9iaWxlTWVudU9wZW4oZmFsc2UpfT5WaWRlb3M8L2E+PC9saT5cbiAgICAgICAgICAgICAgPGxpPjxhIGhyZWY9XCIvdGVjaG5pY2FsXCIgb25DbGljaz17KCkgPT4gc2V0TW9iaWxlTWVudU9wZW4oZmFsc2UpfT5UZWNobmljYWwgSW5mbzwvYT48L2xpPlxuICAgICAgICAgICAgICA8bGk+PGEgaHJlZj1cIi9mYXFzXCIgb25DbGljaz17KCkgPT4gc2V0TW9iaWxlTWVudU9wZW4oZmFsc2UpfT5GQVFzPC9hPjwvbGk+XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvbGk+XG4gICAgICAgIDwvdWw+XG4gICAgICA8L2Rpdj5cbiAgICA8L25hdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInN0eWxlcyIsIk5hdmlnYXRpb24iLCJjbGFzc05hbWUiLCJtb2JpbGVNZW51T3BlbiIsInNldE1vYmlsZU1lbnVPcGVuIiwibW9iaWxlRHJvcGRvd25zIiwic2V0TW9iaWxlRHJvcGRvd25zIiwiY29tcGFueSIsInByb2R1Y3RzIiwiZGlzY292ZXIiLCJuYXYiLCJuYXZpZ2F0aW9uIiwiZGl2IiwibmF2Q29udGFpbmVyIiwibG9nbyIsImgxIiwic3BhbiIsInVsIiwibmF2TWVudSIsImxpIiwiZHJvcGRvd24iLCJhIiwiaHJlZiIsImRyb3Bkb3duVG9nZ2xlIiwiZHJvcGRvd25NZW51IiwibW9iaWxlTWVudVRvZ2dsZSIsImFjdGl2ZSIsIm9uQ2xpY2siLCJoYW1idXJnZXJMaW5lIiwibW9iaWxlTWVudSIsIm1vYmlsZU5hdk1lbnUiLCJtb2JpbGVEcm9wZG93blRvZ2dsZSIsInByZXYiLCJtb2JpbGVEcm9wZG93bk1lbnUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navigation.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CUmer%20Farooq%5CDesktop%5CPatricks%20web%5CCast-Stone%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();