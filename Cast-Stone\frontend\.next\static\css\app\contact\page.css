/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/contact/page.module.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/* Contact Page Styles */

.page_container__5YXRc {
  min-height: 100vh;
  background: var(--background-light);
  color: var(--text-dark);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Navigation */
.page_navigation__enjuy {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

.page_navContainer__juTch {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page_logo__PF8Tu h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
  letter-spacing: -0.02em;
}

.page_logo__PF8Tu span {
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 400;
}

.page_navMenu__fs6c0 {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.page_navMenu__fs6c0 a {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.page_navMenu__fs6c0 a:hover,
.page_navMenu__fs6c0 a.page_active__l9k2n {
  color: var(--primary-color);
}

.page_navMenu__fs6c0 a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.page_navMenu__fs6c0 a:hover::after,
.page_navMenu__fs6c0 a.page_active__l9k2n::after {
  width: 100%;
}

/* Hero Section */
.page_hero__0Vvk_ {
  padding: 8rem 2rem 4rem;
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
}

.page_heroContent__EA5kU {
  max-width: 800px;
  margin: 0 auto;
}

.page_heroTitle__m_yzc {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
}

.page_heroSubtitle__AdiMC {
  font-size: 1.2rem;
  opacity: 0.9;
  line-height: 1.7;
}

/* Contact Section */
.page_contactSection__AuYnq {
  padding: 6rem 2rem;
}

.page_contactContainer___69nj {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

@media (max-width: 968px) {
  .page_contactContainer___69nj {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

/* Form Container */
.page_formContainer__UE5_i {
  background: white;
  padding: 3rem;
  border-radius: 15px;
  box-shadow: var(--shadow);
}

.page_formContainer__UE5_i h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.page_formContainer__UE5_i p {
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.page_form__arM1T {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.page_formRow__qjAzl {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 640px) {
  .page_formRow__qjAzl {
    grid-template-columns: 1fr;
  }
}

.page_formGroup__bIAM3 {
  display: flex;
  flex-direction: column;
}

.page_label__zFMfb {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.page_input__4_GuJ,
.page_textarea__ppW_b,
.page_select__zrwMk {
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.page_input__4_GuJ:focus,
.page_textarea__ppW_b:focus,
.page_select__zrwMk:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.page_textarea__ppW_b {
  resize: vertical;
  min-height: 120px;
}

.page_select__zrwMk {
  cursor: pointer;
}

.page_submitButton__jMO2u {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.page_submitButton__jMO2u:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Contact Info */
.page_contactInfo__jt5gr {
  background: var(--background-light);
  padding: 3rem;
  border-radius: 15px;
}

.page_contactInfo__jt5gr h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
}

.page_contactInfo__jt5gr > p {
  color: var(--text-light);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.page_infoGrid__wjy0H {
  display: grid;
  gap: 2rem;
}

.page_infoItem__dqhjP {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  padding: 1.5rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.page_infoItem__dqhjP:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.page_infoIcon__zNB2y {
  font-size: 1.5rem;
  min-width: 40px;
  text-align: center;
}

.page_infoTitle__OG24k {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.page_infoText__WCFyB {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 0.25rem;
}

.page_infoSubtext__nfiIm {
  color: var(--text-light);
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Footer */
.page_footer__wSOJ4 {
  background: var(--text-dark);
  color: white;
  padding: 3rem 2rem 1rem;
}

.page_footerContent__bl1zS {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.page_footerSection__dqQQL h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.page_footerSection__dqQQL h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.page_footerSection__dqQQL p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.page_footerSection__dqQQL ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.page_footerSection__dqQQL ul li {
  margin-bottom: 0.5rem;
}

.page_footerSection__dqQQL ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.page_footerSection__dqQQL ul li a:hover {
  color: var(--secondary-color);
}

.page_footerBottom__aYKxl {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Mobile Hamburger Menu */
.page_mobileMenuToggle__R_COm {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.page_hamburgerLine__kG_p3 {
  width: 25px;
  height: 3px;
  background: var(--text-dark);
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.page_mobileMenuToggle__R_COm.page_active__l9k2n .page_hamburgerLine__kG_p3:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.page_mobileMenuToggle__R_COm.page_active__l9k2n .page_hamburgerLine__kG_p3:nth-child(2) {
  opacity: 0;
}

.page_mobileMenuToggle__R_COm.page_active__l9k2n .page_hamburgerLine__kG_p3:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.page_mobileMenu__RkvIE {
  position: fixed;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100vh;
  background: white;
  z-index: 1000;
  transition: left 0.3s ease;
  padding-top: 80px;
  overflow-y: auto;
}

.page_mobileMenu__RkvIE.page_active__l9k2n {
  left: 0;
}

.page_mobileNavMenu__D2rCX {
  list-style: none;
  padding: 2rem;
  margin: 0;
}

.page_mobileNavMenu__D2rCX > li {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--background-light);
  padding-bottom: 1rem;
}

.page_mobileNavMenu__D2rCX a {
  display: block;
  padding: 1rem 0;
  color: var(--text-dark);
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 500;
}

.page_mobileDropdownToggle__9PgJp {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.page_mobileDropdownToggle__9PgJp::after {
  content: '+';
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.page_mobileDropdownToggle__9PgJp.page_active__l9k2n::after {
  transform: rotate(45deg);
}

.page_mobileDropdownMenu__wlInf {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: var(--background-light);
  margin-top: 1rem;
  border-radius: 8px;
}

.page_mobileDropdownMenu__wlInf.page_active__l9k2n {
  max-height: 300px;
}

.page_mobileDropdownMenu__wlInf li {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.page_mobileDropdownMenu__wlInf a {
  padding: 0.75rem 1rem;
  font-size: 1rem;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  /* Navigation */
  .page_navMenu__fs6c0 {
    display: none;
  }

  .page_mobileMenuToggle__R_COm {
    display: flex;
  }

  .page_navContainer__juTch {
    padding: 0 1rem;
  }

  /* Hero Section */
  .page_heroTitle__m_yzc {
    font-size: 2.5rem;
  }

  /* Contact Section */
  .page_contactContainer___69nj {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 1rem;
  }

  .page_formContainer__UE5_i,
  .page_contactInfo__jt5gr {
    padding: 2rem;
  }

  .page_formContainer__UE5_i h2,
  .page_contactInfo__jt5gr h2 {
    font-size: 1.5rem;
  }

  .page_formRow__qjAzl {
    grid-template-columns: 1fr;
  }

  /* Footer */
  .page_footerContent__bl1zS {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .page_heroTitle__m_yzc {
    font-size: 2rem;
  }

  .page_formContainer__UE5_i,
  .page_contactInfo__jt5gr {
    padding: 1.5rem;
  }

  .page_contactSection__AuYnq {
    padding: 4rem 1rem;
  }

  .page_hero__0Vvk_ {
    padding: 6rem 1rem 3rem;
  }
}

